import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Bell,
  BellRing,
  Package,
  CreditCard,
  Settings,
  Star,
  Megaphone,
  Trash2,
  Check,
  CheckCheck,
  Clock,
  AlertCircle,
  X,
  Filter,
  ChevronDown,
  Zap,
  Sparkles,
  Crown,
  Target
} from 'lucide-react';
import { useNotificationsStore, type Notification } from '../../../stores/notificationsStore';

// Enhanced Glass Card Component with Extreme Effects
const GlassCard: React.FC<{
  children: React.ReactNode;
  className?: string;
  gradient?: string;
  hoverEffect?: boolean;
  onClick?: () => void;
}> = ({ children, className = '', gradient = 'from-white/10 to-white/5', hoverEffect = true, onClick }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={hoverEffect ? {
      y: -8,
      scale: 1.02,
      boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)"
    } : {}}
    transition={{ type: "spring", stiffness: 300, damping: 30 }}
    onClick={onClick}
    className={`relative bg-gradient-to-br ${gradient} border border-white/30 rounded-3xl shadow-2xl overflow-hidden ${className} ${onClick ? 'cursor-pointer' : ''}`}
    style={{
      zIndex: 10,
      position: 'relative',
    }}
  >
    {/* Enhanced Shimmer effect */}
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-[shimmer_3s_infinite] pointer-events-none" />

    {/* Subtle inner glow */}
    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />

    {/* Content */}
    <div className="relative z-10">
      {children}
    </div>
  </motion.div>
);

// Custom Badge Component
const Badge: React.FC<{
  children: React.ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'error';
  className?: string;
}> = ({ children, variant = 'default', className = '' }) => {
  const variants = {
    default: 'bg-gradient-to-r from-blue-500/25 to-indigo-500/25 text-blue-200 border-blue-400/40 shadow-lg shadow-blue-500/20',
    success: 'bg-gradient-to-r from-emerald-500/25 to-green-500/25 text-emerald-200 border-emerald-400/40 shadow-lg shadow-emerald-500/20',
    warning: 'bg-gradient-to-r from-amber-500/25 to-yellow-500/25 text-amber-200 border-amber-400/40 shadow-lg shadow-amber-500/20',
    error: 'bg-gradient-to-r from-rose-500/25 to-red-500/25 text-rose-200 border-rose-400/40 shadow-lg shadow-rose-500/20',
  };

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-lg text-xs font-semibold border ${variants[variant]} ${className}`}>
      {children}
    </span>
  );
};

const NotificationsPage: React.FC = () => {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAll,
    getNotificationsByType
  } = useNotificationsStore();

  const [selectedFilter, setSelectedFilter] = useState<'all' | Notification['type']>('all');
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const filteredNotifications = selectedFilter === 'all' 
    ? notifications 
    : getNotificationsByType(selectedFilter);

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'order': return Package;
      case 'payment': return CreditCard;
      case 'system': return Settings;
      case 'review': return Star;
      case 'promotion': return Megaphone;
      default: return Bell;
    }
  };

  const getNotificationColor = (type: Notification['type']) => {
    switch (type) {
      case 'order': return 'from-emerald-400 via-green-500 to-teal-600';
      case 'payment': return 'from-blue-400 via-indigo-500 to-purple-600';
      case 'system': return 'from-orange-400 via-amber-500 to-yellow-600';
      case 'review': return 'from-violet-400 via-purple-500 to-fuchsia-600';
      case 'promotion': return 'from-rose-400 via-pink-500 to-red-600';
      default: return 'from-slate-400 via-gray-500 to-zinc-600';
    }
  };

  const getNotificationBorderColor = (type: Notification['type']) => {
    switch (type) {
      case 'order': return 'border-emerald-400/50';
      case 'payment': return 'border-blue-400/50';
      case 'system': return 'border-orange-400/50';
      case 'review': return 'border-violet-400/50';
      case 'promotion': return 'border-rose-400/50';
      default: return 'border-gray-400/50';
    }
  };

  const getNotificationAccentColor = (type: Notification['type']) => {
    switch (type) {
      case 'order': return 'text-emerald-300';
      case 'payment': return 'text-blue-300';
      case 'system': return 'text-orange-300';
      case 'review': return 'text-violet-300';
      case 'promotion': return 'text-rose-300';
      default: return 'text-gray-300';
    }
  };

  const getPriorityColor = (priority: Notification['priority']) => {
    switch (priority) {
      case 'high': return 'bg-gradient-to-r from-red-400 to-rose-500 shadow-lg shadow-red-500/50';
      case 'medium': return 'bg-gradient-to-r from-amber-400 to-orange-500 shadow-lg shadow-amber-500/50';
      case 'low': return 'bg-gradient-to-r from-emerald-400 to-green-500 shadow-lg shadow-emerald-500/50';
      default: return 'bg-gradient-to-r from-slate-400 to-gray-500 shadow-lg shadow-gray-500/50';
    }
  };

  const formatTimeAgo = (timestamp: Date | string) => {
    const now = new Date();
    const date = timestamp instanceof Date ? timestamp : new Date(timestamp);

    // Safety check to ensure we have a valid date
    if (isNaN(date.getTime())) {
      return 'Unknown time';
    }

    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const handleNotificationPress = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }
    // Handle navigation based on notification type
    if (notification.orderId) {
      // Navigate to order details
      console.log('Navigate to order:', notification.orderId);
    }
  };

  const handleDeleteNotification = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm('Are you sure you want to delete this notification?')) {
      deleteNotification(id);
    }
  };

  const handleClearAll = () => {
    if (window.confirm('Are you sure you want to clear all notifications?')) {
      clearAll();
    }
  };

  const filterOptions = [
    { value: 'all', label: 'All Notifications', count: notifications.length },
    { value: 'order', label: 'Orders', count: getNotificationsByType('order').length },
    { value: 'payment', label: 'Payments', count: getNotificationsByType('payment').length },
    { value: 'review', label: 'Reviews', count: getNotificationsByType('review').length },
    { value: 'system', label: 'System', count: getNotificationsByType('system').length },
    { value: 'promotion', label: 'Promotions', count: getNotificationsByType('promotion').length },
  ];

  return (
    <>
      {/* Premium Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-indigo-900 to-purple-900">
        {/* Animated gradient orbs with enhanced colors */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.4, 0.7, 0.4],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-amber-400/40 via-orange-500/40 to-red-500/40 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.5, 0.8, 0.5],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute top-1/4 right-0 w-80 h-80 bg-gradient-to-br from-violet-500/50 via-purple-600/50 to-fuchsia-600/50 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 4
          }}
          className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-cyan-500/40 via-blue-600/40 to-indigo-600/40 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.1, 0.9, 1.1],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 6
          }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-emerald-500/30 via-teal-600/30 to-green-600/30 rounded-full blur-3xl"
        />
      </div>

      <div className="relative z-10 min-h-screen">
        <div className="container mx-auto px-4 py-8 max-w-6xl">
          <div className="space-y-8">
            {/* Ultra Enhanced Notifications Header */}
            <motion.div
              initial={{ opacity: 0, y: -40, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ type: 'spring', damping: 20, stiffness: 300 }}
            >
              <GlassCard
                gradient="from-indigo-500/25 via-purple-500/20 to-pink-500/25"
                className="p-8 border-indigo-400/40 shadow-2xl shadow-purple-500/20"
                hoverEffect={false}
              >
                {/* Enhanced Decorative Background Elements */}
                <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-amber-400/15 via-orange-500/10 to-transparent rounded-full -translate-y-20 translate-x-20" />
                <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-br from-violet-400/10 via-purple-500/8 to-transparent rounded-full translate-y-16 -translate-x-16" />
                <div className="absolute top-1/2 right-1/4 w-24 h-24 bg-gradient-to-br from-cyan-400/8 to-transparent rounded-full" />

                <div className="space-y-6">
                  {/* Main Header Content */}
                  <div className="flex items-center gap-6">
                    <motion.div
                      initial={{ scale: 0, rotate: -180 }}
                      animate={{ scale: 1, rotate: 0 }}
                      transition={{ delay: 0.4, type: 'spring', damping: 15 }}
                      className="relative"
                    >
                      <div className="bg-gradient-to-br from-amber-400/30 via-orange-500/20 to-red-500/30 border-2 border-amber-300/40 rounded-2xl p-4 shadow-lg shadow-orange-500/20">
                        <Bell size={36} className="text-amber-100" />
                      </div>

                      {unreadCount > 0 && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ delay: 0.6, type: 'spring', damping: 10 }}
                          className="absolute -top-2 -right-2"
                        >
                          <div className="bg-gradient-to-r from-red-500 to-red-600 border-2 border-white rounded-full px-2 py-1">
                            <span className="text-white text-xs font-bold">
                              {unreadCount > 99 ? '99+' : unreadCount}
                            </span>
                          </div>
                        </motion.div>
                      )}
                    </motion.div>

                    <div className="flex-1 space-y-2">
                      <motion.h1
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.6, duration: 0.6 }}
                        className="text-white text-4xl font-black"
                      >
                        Notifications
                      </motion.h1>

                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.7, duration: 0.6 }}
                        className="flex items-center gap-3"
                      >
                        {unreadCount > 0 ? (
                          <>
                            <Badge variant="error">
                              {unreadCount} NEW
                            </Badge>
                            <span className="text-white/90 font-medium">
                              You have unread messages
                            </span>
                          </>
                        ) : (
                          <>
                            <Badge variant="success">
                              ✓ ALL READ
                            </Badge>
                            <span className="text-white/90 font-medium">
                              You're all caught up!
                            </span>
                          </>
                        )}
                      </motion.div>
                    </div>
                  </div>

                  {/* Enhanced Stats Preview */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8, duration: 0.6 }}
                  >
                    <div className="flex justify-between items-center">
                      <div className="text-center">
                        <div className="text-white/80 text-sm font-medium">TOTAL</div>
                        <div className="text-white text-2xl font-black">{notifications.length}</div>
                      </div>

                      <div className="w-px h-12 bg-white/30" />

                      <div className="text-center">
                        <div className="text-white/80 text-sm font-medium">UNREAD</div>
                        <div className="text-white text-2xl font-black">{unreadCount}</div>
                      </div>

                      <div className="w-px h-12 bg-white/30" />

                      <div className="text-center">
                        <div className="text-white/80 text-sm font-medium">TODAY</div>
                        <div className="text-white text-2xl font-black">
                          {notifications.filter(n => {
                            const today = new Date();
                            const notifDate = n.timestamp instanceof Date ? n.timestamp : new Date(n.timestamp);
                            return !isNaN(notifDate.getTime()) && notifDate.toDateString() === today.toDateString();
                          }).length}
                        </div>
                      </div>
                    </div>
                  </motion.div>

                  {/* Enhanced Action Buttons */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.9, duration: 0.5 }}
                  >
                    <div className="flex justify-center gap-4">
                      {unreadCount > 0 && (
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={markAllAsRead}
                          className="flex items-center gap-2 bg-gradient-to-r from-emerald-500/20 to-green-500/20 hover:from-emerald-500/30 hover:to-green-500/30 border-2 border-emerald-400/40 text-emerald-100 px-6 py-3 rounded-2xl font-bold transition-all duration-300 shadow-lg shadow-emerald-500/20"
                        >
                          <CheckCheck size={18} />
                          Mark All Read
                        </motion.button>
                      )}

                      {notifications.length > 0 && (
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={handleClearAll}
                          className="flex items-center gap-2 bg-gradient-to-r from-rose-500/20 to-red-500/20 hover:from-rose-500/30 hover:to-red-500/30 border-2 border-rose-400/40 text-rose-100 px-6 py-3 rounded-2xl font-bold transition-all duration-300 shadow-lg shadow-rose-500/20"
                        >
                          <Trash2 size={18} />
                          Clear All
                        </motion.button>
                      )}
                    </div>
                  </motion.div>
                </div>
              </GlassCard>
            </motion.div>

            {/* Filter Section */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              <GlassCard
                className="p-6 border-violet-400/30"
                gradient="from-violet-500/15 via-purple-500/10 to-indigo-500/15"
              >
                <div className="flex items-center justify-between">
                  <h3 className="text-violet-100 text-xl font-bold">Filter Notifications</h3>

                  <div className="relative">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setIsFilterOpen(!isFilterOpen)}
                      className="flex items-center gap-2 bg-gradient-to-r from-violet-500/20 to-purple-500/20 hover:from-violet-500/30 hover:to-purple-500/30 border border-violet-400/40 text-violet-100 px-4 py-2 rounded-xl font-medium transition-all duration-300 shadow-lg shadow-violet-500/20"
                    >
                      <Filter size={16} />
                      {filterOptions.find(opt => opt.value === selectedFilter)?.label}
                      <ChevronDown size={16} className={`transition-transform duration-300 ${isFilterOpen ? 'rotate-180' : ''}`} />
                    </motion.button>

                    <AnimatePresence>
                      {isFilterOpen && (
                        <motion.div
                          initial={{ opacity: 0, y: -10, scale: 0.95 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: -10, scale: 0.95 }}
                          transition={{ duration: 0.2 }}
                          className="absolute top-full right-0 mt-2 w-64 bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl z-50"
                        >
                          <div className="p-2">
                            {filterOptions.map((option) => (
                              <motion.button
                                key={option.value}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                onClick={() => {
                                  setSelectedFilter(option.value as any);
                                  setIsFilterOpen(false);
                                }}
                                className={`w-full flex items-center justify-between p-3 rounded-xl transition-all duration-300 ${
                                  selectedFilter === option.value
                                    ? 'bg-gradient-to-r from-purple-500 to-violet-600 text-white'
                                    : 'text-white/80 hover:bg-white/10'
                                }`}
                              >
                                <span className="font-medium">{option.label}</span>
                                <Badge variant={selectedFilter === option.value ? 'default' : 'default'}>
                                  {option.count}
                                </Badge>
                              </motion.button>
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              </GlassCard>
            </motion.div>

            {/* Notifications List */}
            <div className="space-y-4">
              <AnimatePresence>
                {filteredNotifications.length > 0 ? (
                  filteredNotifications.map((notification, index) => (
                    <motion.div
                      key={notification.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      transition={{ delay: index * 0.1, duration: 0.4 }}
                    >
                      <GlassCard
                        className={`p-6 transition-all duration-300 ${
                          notification.read
                            ? 'border-slate-400/30 bg-gradient-to-br from-slate-500/5 to-gray-500/5'
                            : `border-2 ${getNotificationBorderColor(notification.type)} bg-gradient-to-br ${getNotificationColor(notification.type)}/15 shadow-lg`
                        }`}
                        hoverEffect={true}
                        onClick={() => handleNotificationPress(notification)}
                      >
                        <div className="flex items-start gap-4">
                          {/* Enhanced Icon */}
                          <motion.div
                            initial={{ scale: 0.8, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ delay: index * 0.05, type: 'spring', damping: 15 }}
                          >
                            <div className={`bg-gradient-to-br ${getNotificationColor(notification.type)} p-3 rounded-2xl border-2 ${
                              notification.read ? 'border-white/20 opacity-70' : 'border-white/40 shadow-lg'
                            } ${notification.read ? '' : `shadow-${notification.type === 'order' ? 'emerald' : notification.type === 'payment' ? 'blue' : notification.type === 'system' ? 'orange' : notification.type === 'review' ? 'violet' : 'rose'}-500/30`}`}>
                              {React.createElement(getNotificationIcon(notification.type), {
                                size: 22,
                                className: "text-white"
                              })}
                            </div>
                          </motion.div>

                          {/* Content */}
                          <div className="flex-1 space-y-2">
                            <div className="flex items-center justify-between">
                              <h4 className={`font-bold ${
                                notification.read ? 'text-slate-300' : `${getNotificationAccentColor(notification.type)} text-shadow-sm`
                              }`}>
                                {notification.title}
                              </h4>

                              <div className="flex items-center gap-2">
                                {/* Priority Indicator */}
                                <motion.div
                                  className={`w-3 h-3 rounded-full ${getPriorityColor(notification.priority)}`}
                                  animate={notification.priority === 'high' && !notification.read ? {
                                    scale: [1, 1.2, 1],
                                    opacity: [1, 0.8, 1]
                                  } : {}}
                                  transition={{
                                    duration: 2,
                                    repeat: Infinity,
                                    ease: "easeInOut"
                                  }}
                                />

                                {/* Delete Button */}
                                <motion.button
                                  whileHover={{ scale: 1.1 }}
                                  whileTap={{ scale: 0.9 }}
                                  onClick={(e) => handleDeleteNotification(notification.id, e)}
                                  className="p-1 rounded-full hover:bg-white/10 transition-colors duration-200"
                                >
                                  <X size={16} className="text-white/60 hover:text-white" />
                                </motion.button>
                              </div>
                            </div>

                            <p className={`text-sm ${
                              notification.read ? 'text-white/50' : 'text-white/80'
                            }`}>
                              {notification.message}
                            </p>

                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2 text-white/40 text-xs">
                                <Clock size={12} />
                                {formatTimeAgo(notification.timestamp)}
                              </div>

                              {!notification.read && (
                                <motion.div
                                  animate={{
                                    scale: [1, 1.05, 1],
                                    opacity: [1, 0.9, 1]
                                  }}
                                  transition={{
                                    duration: 2,
                                    repeat: Infinity,
                                    ease: "easeInOut"
                                  }}
                                >
                                  <Badge variant="error">
                                    New
                                  </Badge>
                                </motion.div>
                              )}
                            </div>
                          </div>
                        </div>
                      </GlassCard>
                    </motion.div>
                  ))
                ) : (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <GlassCard
                      className="p-12 text-center border-slate-400/30"
                      gradient="from-slate-500/10 via-gray-500/5 to-zinc-500/10"
                    >
                      <div className="space-y-4">
                        <motion.div
                          className="bg-gradient-to-br from-slate-400/20 to-gray-500/20 rounded-2xl p-4 w-16 h-16 mx-auto flex items-center justify-center border border-slate-400/30"
                          animate={{
                            scale: [1, 1.05, 1],
                            opacity: [0.7, 1, 0.7]
                          }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                        >
                          <BellRing size={32} className="text-slate-300" />
                        </motion.div>
                        <div className="space-y-2">
                          <h4 className="text-slate-200 text-xl font-bold">No Notifications</h4>
                          <p className="text-slate-400">
                            {selectedFilter === 'all'
                              ? "You're all caught up! No new notifications."
                              : `No ${selectedFilter} notifications found.`
                            }
                          </p>
                        </div>
                      </div>
                    </GlassCard>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default NotificationsPage;
